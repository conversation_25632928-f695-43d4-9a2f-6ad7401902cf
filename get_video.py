import subprocess
import random
import string
import time
import os
import glob
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor, as_completed

# 代理配置
PROXY_HOST = "novabpm-ins-7ws6jjkn.novada.pro"
PROXY_PORT = 7788
PROXY_USER = "5851b070f69-zone-adam"
PROXY_PASS = "t5gNbn29fwu2Rlhc"

# 文件配置
VIDEO_LIST_FILE = "video_list.txt"
SUCCESS_LOG = "success.log"
FAIL_LOG = "fail.log"

# 并发配置
MAX_WORKERS = 4
DELAY_BETWEEN_DOWNLOADS = 2
VIDEO_FORMAT = "bestvideo[height<=480]+bestaudio/best[height<=480]"


def random_session(length=16):
    """生成随机 session 名称"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))


def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.2f} {size_names[i]}"


def get_downloaded_file_size(url, download_dir="."):
    """获取下载文件的大小"""
    try:
        # 获取视频ID
        video_id = url.split('v=')[-1].split('&')[0] if 'v=' in url else url.split('/')[-1]

        # 查找可能的文件名模式
        patterns = [
            f"*{video_id}*",
            f"*{video_id[:11]}*"  # YouTube视频ID通常是11位
        ]

        total_size = 0
        found_files = []

        for pattern in patterns:
            files = glob.glob(os.path.join(download_dir, pattern))
            for file_path in files:
                if os.path.isfile(file_path):
                    file_size = os.path.getsize(file_path)
                    total_size += file_size
                    found_files.append((file_path, file_size))

        return total_size, found_files
    except Exception:
        return 0, []


def write_log(log_file, message):
    """线程安全的日志写入"""
    import fcntl
    try:
        with open(log_file, "a") as f:
            fcntl.flock(f.fileno(), fcntl.LOCK_EX)
            f.write(message)
            fcntl.flock(f.fileno(), fcntl.LOCK_UN)
    except Exception as e:
        print(f"写入日志失败: {e}")


def download_video(args):
    """下载单个视频 - 适配多进程"""
    url, worker_id = args
    session_id = random_session()
    proxy_url = f"http://{PROXY_USER}-session-{session_id}:{PROXY_PASS}@{PROXY_HOST}:{PROXY_PORT}"

    cmd = [
        "yt-dlp",
        "-f", VIDEO_FORMAT,
        "--proxy", proxy_url,
        url
    ]

    # 添加随机延迟，避免同时启动过多请求
    time.sleep(random.uniform(0, DELAY_BETWEEN_DOWNLOADS))

    try:
        # 记录下载前的时间
        download_start = time.time()

        subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # 获取下载文件大小
        file_size, downloaded_files = get_downloaded_file_size(url)
        download_time = time.time() - download_start

        # 记录成功日志，包含文件大小信息
        size_str = format_file_size(file_size)
        log_message = f"{datetime.now()} | Worker-{worker_id} | {url} | session={session_id} | OK | 大小: {size_str} | 耗时: {download_time:.2f}s\n"
        write_log(SUCCESS_LOG, log_message)

        print(f"Worker-{worker_id} 成功下载: {url} | 大小: {size_str}")

        return {
            "url": url,
            "status": "success",
            "worker_id": worker_id,
            "file_size": file_size,
            "download_time": download_time,
            "files": downloaded_files
        }
    except subprocess.CalledProcessError as e:
        download_time = time.time() - download_start if 'download_start' in locals() else 0
        log_message = f"{datetime.now()} | Worker-{worker_id} | {url} | session={session_id} | FAIL | 错误码: {e.returncode} | 耗时: {download_time:.2f}s\n"
        write_log(FAIL_LOG, log_message)

        return {
            "url": url,
            "status": "failed",
            "worker_id": worker_id,
            "error_code": e.returncode,
            "file_size": 0,
            "download_time": download_time
        }


def main():
    # 读取视频列表
    with open(VIDEO_LIST_FILE, "r") as f:
        urls = [line.strip() for line in f if line.strip()]

    print(f"开始多进程下载，共 {len(urls)} 个视频，使用 {MAX_WORKERS} 个进程...")

    # 准备参数：为每个URL分配worker ID
    download_args = [(url, i % MAX_WORKERS + 1) for i, url in enumerate(urls)]

    success, fail = 0, 0
    total_size = 0  # 总下载大小
    total_download_time = 0  # 总下载时间
    start_time = time.time()

    # 使用进程池进行并发下载
    with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 提交所有任务
        future_to_url = {executor.submit(download_video, args): args[0] for args in download_args}

        # 处理完成的任务
        for future in as_completed(future_to_url):
            url = future_to_url[future]
            try:
                result = future.result()
                if result["status"] == "success":
                    success += 1
                    total_size += result.get("file_size", 0)
                    total_download_time += result.get("download_time", 0)
                else:
                    fail += 1

                # 显示进度
                total_completed = success + fail
                progress = (total_completed / len(urls)) * 100
                current_size_str = format_file_size(total_size)
                print(f"进度: {total_completed}/{len(urls)} ({progress:.1f}%) - ✅ {success} 成功, ❌ {fail} 失败 | 已下载: {current_size_str}")

            except Exception as e:
                fail += 1
                print(f"❌ 处理任务时发生异常: {e}")

    end_time = time.time()
    duration = end_time - start_time

    # 格式化统计信息
    total_size_str = format_file_size(total_size)
    avg_download_speed = total_size / total_download_time if total_download_time > 0 else 0
    avg_download_speed_str = format_file_size(avg_download_speed) + "/s" if avg_download_speed > 0 else "N/A"

    print(f"\n🎉 下载完成!")
    print(f"📊 统计信息:")
    print(f"   ✅ 成功: {success} 个")
    print(f"   ❌ 失败: {fail} 个")
    print(f"   📁 总大小: {total_size_str}")
    print(f"   ⏱️  总耗时: {duration:.2f} 秒")
    print(f"   🚀 任务速度: {len(urls)/duration:.2f} 个/秒")
    print(f"   📶 下载速度: {avg_download_speed_str}")


if __name__ == "__main__":
    main()

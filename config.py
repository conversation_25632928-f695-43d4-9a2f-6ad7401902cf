# 多进程下载配置文件

# 并发配置
MAX_WORKERS = 4  # 最大并发进程数，建议根据你的网络和系统性能调整
DELAY_BETWEEN_DOWNLOADS = 2  # 每个下载之间的随机延迟上限（秒），避免触发限制

# 代理配置
PROXY_HOST = "novabpm-ins-7ws6jjkn.novada.pro"
PROXY_PORT = 7788
PROXY_USER = "5851b070f69-zone-adam"
PROXY_PASS = "t5gNbn29fwu2Rlhc"

# 文件配置
VIDEO_LIST_FILE = "video_list.txt"
SUCCESS_LOG = "success.log"
FAIL_LOG = "fail.log"

# yt-dlp 下载配置
VIDEO_FORMAT = "bestvideo[height<=480]+bestaudio/best[height<=480]"  # 视频质量设置

# 性能建议：
# - MAX_WORKERS: 2-8 个进程通常效果最好，过多可能导致网络拥塞
# - DELAY_BETWEEN_DOWNLOADS: 1-5 秒，太短可能触发限制，太长影响效率
# - 如果经常失败，可以增加延迟时间或减少并发数

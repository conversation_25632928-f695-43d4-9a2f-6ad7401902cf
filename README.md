# YouTube 视频多进程下载器

这是一个支持多进程并发下载的 YouTube 视频下载脚本，基于 yt-dlp 和代理服务。

## 功能特点

- ✅ **多进程并发下载**：支持同时运行多个下载进程，大幅提升下载效率
- ✅ **智能延迟控制**：随机延迟避免触发平台限制
- ✅ **线程安全日志**：并发环境下的安全日志记录
- ✅ **实时进度显示**：显示下载进度和统计信息
- ✅ **代理支持**：使用代理服务避免地区限制
- ✅ **配置文件支持**：可通过配置文件调整参数

## 文件说明

- `get_video.py` - 主下载脚本
- `config.py` - 配置文件（可选）
- `video_list.txt` - 视频URL列表文件
- `success.log` - 成功下载日志
- `fail.log` - 失败下载日志

## 使用方法

### 1. 准备视频列表
创建 `video_list.txt` 文件，每行一个 YouTube 视频URL：
```
https://www.youtube.com/watch?v=VIDEO_ID_1
https://www.youtube.com/watch?v=VIDEO_ID_2
https://www.youtube.com/watch?v=VIDEO_ID_3
```

### 2. 配置参数（可选）
编辑 `config.py` 文件调整下载参数：
```python
MAX_WORKERS = 4  # 并发进程数
DELAY_BETWEEN_DOWNLOADS = 2  # 延迟时间（秒）
```

### 3. 运行下载
```bash
python get_video.py
```

## 配置说明

### 并发配置
- `MAX_WORKERS`: 最大并发进程数（建议 2-8）
- `DELAY_BETWEEN_DOWNLOADS`: 每个下载的随机延迟上限（秒）

### 性能建议
- **并发数**: 2-8 个进程通常效果最好，过多可能导致网络拥塞
- **延迟时间**: 1-5 秒，太短可能触发限制，太长影响效率
- **网络状况**: 如果经常失败，可以增加延迟时间或减少并发数

## 输出示例

```
开始多进程下载，共 10 个视频，使用 4 个进程...
✅ Worker-1 成功下载: https://www.youtube.com/watch?v=VIDEO_ID_1
✅ Worker-2 成功下载: https://www.youtube.com/watch?v=VIDEO_ID_2
进度: 2/10 (20.0%) - ✅ 2 成功, ❌ 0 失败
...
🎉 下载完成!
📊 统计: ✅ 8 成功, ❌ 2 失败
⏱️  总耗时: 120.45 秒
🚀 平均速度: 0.08 个/秒
```

## 注意事项

1. 确保已安装 `yt-dlp`：`pip install yt-dlp`
2. 代理配置需要有效的代理服务
3. 下载的视频文件会保存在当前目录
4. 日志文件会记录详细的下载信息
5. 建议根据网络状况调整并发数和延迟时间

## 故障排除

- **下载失败率高**: 增加 `DELAY_BETWEEN_DOWNLOADS` 或减少 `MAX_WORKERS`
- **速度太慢**: 适当增加 `MAX_WORKERS`（不超过 8）
- **代理错误**: 检查代理配置是否正确
- **文件权限错误**: 确保有写入当前目录的权限
